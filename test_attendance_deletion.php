<?php
/**
 * Test script to verify that permanent_diapers (attendance records) are deleted 
 * when contract type changes between open/closed status
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

// Database connection
$file = fopen(__DIR__ . "/Pages/connection/one.txt", "r");
if (!$file) {
    die('Error reading configuration file');
}

$servername = trim(fgets($file));
$username = trim(fgets($file));
$password = trim(fgets($file));
$dbname = trim(fgets($file));
fclose($file);

$conn = new mysqli($servername, $username, $password, $dbname);
$conn->set_charset("utf8");

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

echo "<h2>Attendance Records Deletion Test</h2>\n";

// Function to test attendance deletion logic
function testAttendanceDeletion($conn, $contract_id, $new_contract_type) {
    echo "<h3>Testing Contract ID: $contract_id</h3>\n";
    
    // Get current contract details
    $current_contract_sql = "SELECT contract_type, end_date_contract FROM contract WHERE id_contract = ?";
    $current_stmt = $conn->prepare($current_contract_sql);
    if (!$current_stmt) {
        echo "Error preparing current contract query: " . $conn->error . "\n";
        return false;
    }
    
    $current_stmt->bind_param("i", $contract_id);
    $current_stmt->execute();
    $current_result = $current_stmt->get_result();
    $current_contract = $current_result->fetch_assoc();
    $current_stmt->close();
    
    if (!$current_contract) {
        echo "Contract not found!\n";
        return false;
    }
    
    // Determine current frontend contract type (1-4)
    $current_frontend_type = $current_contract['contract_type']; // 1=monthly, 2=daily
    if ($current_frontend_type == 1) { // Monthly
        $current_frontend_type = $current_contract['end_date_contract'] ? 2 : 1; // 2=closed, 1=open
    } else { // Daily
        $current_frontend_type = $current_contract['end_date_contract'] ? 4 : 3; // 4=closed, 3=open
    }
    
    echo "Current frontend contract type: $current_frontend_type\n";
    echo "New frontend contract type: $new_contract_type\n";
    
    // Map frontend types to descriptions
    $type_descriptions = [
        1 => 'Monthly Open (شهري مفتوح)',
        2 => 'Monthly Closed (شهري مغلق)',
        3 => 'Daily Open (يومي مفتوح)',
        4 => 'Daily Closed (يومي مغلق)'
    ];
    
    echo "Current type: " . $type_descriptions[$current_frontend_type] . "\n";
    echo "New type: " . $type_descriptions[$new_contract_type] . "\n";
    
    // Count permanent_diapers before
    $count_sql = "SELECT COUNT(*) as count FROM permanent_diapers WHERE id_contract = ?";
    $count_stmt = $conn->prepare($count_sql);
    $count_stmt->bind_param("i", $contract_id);
    $count_stmt->execute();
    $count_result = $count_stmt->get_result();
    $before_count = $count_result->fetch_assoc()['count'];
    $count_stmt->close();
    
    echo "Attendance records before: $before_count\n";
    
    // Check if there's an open/closed status change that requires permanent_diapers deletion
    $requires_attendance_deletion = false;
    
    // Define the conditions for permanent_diapers deletion
    if (($current_frontend_type == 2 && ($new_contract_type == 1 || $new_contract_type == 3)) || // Monthly Closed → Monthly Open or Daily Open
        ($current_frontend_type == 1 && ($new_contract_type == 2 || $new_contract_type == 4)) || // Monthly Open → Monthly Closed or Daily Closed
        ($current_frontend_type == 3 && ($new_contract_type == 2 || $new_contract_type == 4)) || // Daily Open → Monthly Closed or Daily Closed
        ($current_frontend_type == 4 && ($new_contract_type == 1 || $new_contract_type == 3))) {  // Daily Closed → Monthly Open or Daily Open
        $requires_attendance_deletion = true;
    }
    
    echo "Requires attendance deletion: " . ($requires_attendance_deletion ? "YES" : "NO") . "\n";
    
    if ($requires_attendance_deletion) {
        echo "Attendance records should be deleted due to open/closed status change\n";
        
        // Delete attendance records (simulating the logic from create_contract_con.php)
        $delete_attendance_sql = "DELETE FROM permanent_diapers WHERE id_contract = ?";
        $delete_attendance_stmt = $conn->prepare($delete_attendance_sql);
        if (!$delete_attendance_stmt) {
            echo "Error preparing delete query: " . $conn->error . "\n";
            return false;
        }
        
        $delete_attendance_stmt->bind_param("i", $contract_id);
        if (!$delete_attendance_stmt->execute()) {
            echo "Error deleting attendance records: " . $delete_attendance_stmt->error . "\n";
            return false;
        }
        
        $deleted_count = $delete_attendance_stmt->affected_rows;
        $delete_attendance_stmt->close();
        
        echo "Deleted $deleted_count attendance records\n";
    } else {
        echo "No attendance deletion needed - open/closed status not changing in required way\n";
    }
    
    // Count permanent_diapers after
    $count_stmt = $conn->prepare($count_sql);
    $count_stmt->bind_param("i", $contract_id);
    $count_stmt->execute();
    $count_result = $count_stmt->get_result();
    $after_count = $count_result->fetch_assoc()['count'];
    $count_stmt->close();
    
    echo "Attendance records after: $after_count\n";
    echo "---\n";
    
    return true;
}

// Get some sample contracts to test with
$sample_contracts_sql = "SELECT c.id_contract, c.contract_type, c.end_date_contract, e.name_ar_contract 
                        FROM contract c 
                        LEFT JOIN employees e ON c.id_employees = e.id_employees 
                        LIMIT 5";
$result = $conn->query($sample_contracts_sql);

if ($result->num_rows > 0) {
    echo "<h3>Available Contracts for Testing:</h3>\n";
    while ($row = $result->fetch_assoc()) {
        $frontend_type = $row['contract_type']; // 1=monthly, 2=daily
        if ($frontend_type == 1) { // Monthly
            $frontend_type = $row['end_date_contract'] ? 2 : 1; // 2=closed, 1=open
        } else { // Daily
            $frontend_type = $row['end_date_contract'] ? 4 : 3; // 4=closed, 3=open
        }
        
        $type_descriptions = [
            1 => 'Monthly Open',
            2 => 'Monthly Closed', 
            3 => 'Daily Open',
            4 => 'Daily Closed'
        ];
        
        echo "Contract ID: " . $row['id_contract'] . 
             ", Employee: " . ($row['name_ar_contract'] ?? 'N/A') . 
             ", Current Type: " . $type_descriptions[$frontend_type] . " ($frontend_type)\n";
    }
    echo "\n";
    
    // Test scenarios
    echo "<h3>Test Scenarios:</h3>\n";
    
    // Reset result pointer
    $result->data_seek(0);
    $first_contract = $result->fetch_assoc();
    $contract_id = $first_contract['id_contract'];
    
    // Test case 1: Monthly Closed → Monthly Open (should delete)
    echo "<h4>Test Case 1: Monthly Closed → Monthly Open (should delete attendance)</h4>\n";
    testAttendanceDeletion($conn, $contract_id, 1);
    
    // Test case 2: Monthly Open → Daily Closed (should delete)
    echo "<h4>Test Case 2: Monthly Open → Daily Closed (should delete attendance)</h4>\n";
    testAttendanceDeletion($conn, $contract_id, 4);
    
    // Test case 3: Daily Open → Daily Closed (should delete)
    echo "<h4>Test Case 3: Daily Open → Daily Closed (should delete attendance)</h4>\n";
    testAttendanceDeletion($conn, $contract_id, 4);
    
} else {
    echo "No contracts found in database\n";
}

$conn->close();
?>
