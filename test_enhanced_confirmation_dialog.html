<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Enhanced Contract Type Change Confirmation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Enhanced Contract Type Change Confirmation</h5>
                    </div>
                    <div class="card-body">
                        <form id="testForm" onsubmit="return validateForm()">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="contract_id" class="form-label">Contract ID (0 = new, >0 = update)</label>
                                        <input type="number" class="form-control" id="contract_id" value="0">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="contract_type" class="form-label">Contract Type</label>
                                        <select class="form-control" id="contract_type">
                                            <option value="">Select Contract Type</option>
                                            <option value="1">1 - Monthly Open (شهري مفتوح)</option>
                                            <option value="2">2 - Monthly Closed (شهري مغلق)</option>
                                            <option value="3">3 - Daily Open (يومي مفتوح)</option>
                                            <option value="4">4 - Daily Closed (يومي مغلق)</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-secondary" onclick="simulateContractLoad(1)">
                                        Load Monthly Open (1)
                                    </button>
                                    <button type="button" class="btn btn-secondary" onclick="simulateContractLoad(2)">
                                        Load Monthly Closed (2)
                                    </button>
                                    <button type="button" class="btn btn-secondary" onclick="simulateContractLoad(3)">
                                        Load Daily Open (3)
                                    </button>
                                    <button type="button" class="btn btn-secondary" onclick="simulateContractLoad(4)">
                                        Load Daily Closed (4)
                                    </button>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <button type="submit" class="btn btn-primary">
                                    Test Submit (Check Confirmation Dialog)
                                </button>
                            </div>
                        </form>
                        
                        <div class="mt-4">
                            <h6><strong>Test Scenarios:</strong></h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-success">Should Show Attendance Warning:</h6>
                                    <ul class="small">
                                        <li>Monthly Closed (2) → Monthly Open (1)</li>
                                        <li>Monthly Closed (2) → Daily Open (3)</li>
                                        <li>Monthly Open (1) → Monthly Closed (2)</li>
                                        <li>Monthly Open (1) → Daily Closed (4)</li>
                                        <li>Daily Open (3) → Monthly Closed (2)</li>
                                        <li>Daily Open (3) → Daily Closed (4)</li>
                                        <li>Daily Closed (4) → Monthly Open (1)</li>
                                        <li>Daily Closed (4) → Daily Open (3)</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-warning">Should NOT Show Attendance Warning:</h6>
                                    <ul class="small">
                                        <li>Monthly Open (1) → Daily Open (3)</li>
                                        <li>Monthly Closed (2) → Daily Closed (4)</li>
                                        <li>Daily Open (3) → Monthly Open (1)</li>
                                        <li>Daily Closed (4) → Monthly Closed (2)</li>
                                        <li>Same type changes (no change)</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <div class="alert alert-info">
                                <p><strong>Instructions:</strong></p>
                                <ol>
                                    <li>Click one of the "Load" buttons to simulate loading a contract</li>
                                    <li>Change contract type to test different scenarios</li>
                                    <li>Click "Test Submit" to see the confirmation dialog</li>
                                    <li>Check if the attendance warning appears for the correct scenarios</li>
                                </ol>
                            </div>
                        </div>
                        
                        <div id="results" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Variable to store original contract type for comparison
        let originalContractType = null;
        
        // Function to check if attendance deletion is required based on contract type change
        function checkAttendanceDeletionRequired(originalType, newType) {
            // Convert string values to integers
            const original = parseInt(originalType);
            const newVal = parseInt(newType);
            
            // Define the conditions for permanent_diapers deletion
            // Monthly Closed (2) → Monthly Open (1) or Daily Open (3)
            if (original === 2 && (newVal === 1 || newVal === 3)) return true;
            
            // Monthly Open (1) → Monthly Closed (2) or Daily Closed (4)
            if (original === 1 && (newVal === 2 || newVal === 4)) return true;
            
            // Daily Open (3) → Monthly Closed (2) or Daily Closed (4)
            if (original === 3 && (newVal === 2 || newVal === 4)) return true;
            
            // Daily Closed (4) → Monthly Open (1) or Daily Open (3)
            if (original === 4 && (newVal === 1 || newVal === 3)) return true;
            
            return false;
        }
        
        function simulateContractLoad(contractType) {
            // Simulate loading a contract with specified type
            $('#contract_type').val(contractType.toString());
            $('#contract_id').val('123'); // Set to update mode
            originalContractType = contractType.toString(); // Store original contract type
            
            const typeNames = {
                1: 'Monthly Open (شهري مفتوح)',
                2: 'Monthly Closed (شهري مغلق)',
                3: 'Daily Open (يومي مفتوح)',
                4: 'Daily Closed (يومي مغلق)'
            };
            
            $('#results').html(`<div class="alert alert-success">Contract loaded! Original type set to ${contractType} - ${typeNames[contractType]}</div>`);
        }
        
        function validateForm() {
            const contractId = $('#contract_id').val();
            const isUpdate = contractId > 0;
            
            // Check if contract type has changed and show confirmation dialog
            if (isUpdate && originalContractType !== null) {
                const currentContractType = $('#contract_type').val();
                if (originalContractType !== currentContractType) {
                    // Check if there's an open/closed status change that requires permanent_diapers deletion
                    const requiresAttendanceDeletion = checkAttendanceDeletionRequired(originalContractType, currentContractType);
                    
                    let confirmMessage = 'تحذير: سيؤدي تغيير نوع العقد إلى حذف جميع تقارير الإنجاز المرتبطة بهذا العقد (إن وجدت) ويجب إعادة إنشاؤها.';
                    
                    if (requiresAttendanceDeletion) {
                        confirmMessage += '\n\nكما سيؤدي تغيير حالة العقد من مفتوح إلى مغلق أو العكس إلى حذف جميع سجلات الحضور المرتبطة بهذا العقد (إن وجدت) ويجب إعادة إنشاؤها.';
                    }
                    
                    confirmMessage += '\n\nهل تريد المتابعة؟';
                    
                    // Show result before confirmation
                    const typeNames = {
                        1: 'Monthly Open (شهري مفتوح)',
                        2: 'Monthly Closed (شهري مغلق)',
                        3: 'Daily Open (يومي مفتوح)',
                        4: 'Daily Closed (يومي مغلق)'
                    };
                    
                    let resultHtml = `<div class="alert alert-info">
                        <strong>Test Result:</strong><br>
                        Original Type: ${originalContractType} - ${typeNames[originalContractType]}<br>
                        New Type: ${currentContractType} - ${typeNames[currentContractType]}<br>
                        Attendance Deletion Required: <span class="badge ${requiresAttendanceDeletion ? 'bg-danger' : 'bg-success'}">${requiresAttendanceDeletion ? 'YES' : 'NO'}</span>
                    </div>`;
                    
                    $('#results').html(resultHtml);
                    
                    if (!confirm(confirmMessage)) {
                        return false; // User cancelled the operation
                    }
                }
            }
            
            $('#results').append('<div class="alert alert-success">Form validation passed! In real implementation, form would be submitted.</div>');
            return false; // Prevent actual form submission for testing
        }
    </script>
</body>
</html>
