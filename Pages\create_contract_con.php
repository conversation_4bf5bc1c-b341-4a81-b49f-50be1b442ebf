<?php
// بداية جلسة المستخدم وإعدادات عرض الأخطاء
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// معالجة طلب AJAX للحصول على مهام الوظيفة
if (isset($_GET['action']) && $_GET['action'] === 'get_job_tasks') {
    header('Content-Type: application/json');
    try {
        if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
            throw new Exception('Invalid job title ID');
        }

        $jobTitleId = intval($_GET['id']);

        // قراءة تفاصيل الاتصال بقاعدة البيانات
        $file = fopen(__DIR__ . "/connection/one.txt", "r");
        if (!$file) {
            throw new Exception('Error reading configuration file');
        }

        $servername = trim(fgets($file));
        $username = trim(fgets($file));
        $password = trim(fgets($file));
        $dbname = trim(fgets($file));
        fclose($file);

        // إنشاء اتصال بقاعدة البيانات
        $conn = new mysqli($servername, $username, $password, $dbname);
        $conn->set_charset("utf8");

        if ($conn->connect_error) {
            throw new Exception("Database connection failed: " . $conn->connect_error);
        }

        // الحصول على بيانات المسمى الوظيفي
        $stmt = $conn->prepare("SELECT data_todo_list_Job FROM Job_titles WHERE id_Job_titles = ?");
        if (!$stmt) {
            throw new Exception("Query preparation failed: " . $conn->error);
        }

        $stmt->bind_param("i", $jobTitleId);
        if (!$stmt->execute()) {
            throw new Exception("Query execution failed: " . $stmt->error);
        }

        $result = $stmt->get_result();
        if ($row = $result->fetch_assoc()) {
            $todoList = json_decode($row['data_todo_list_Job'], true);
            $tasks = array_map(function($task) {
                return $task['taskName'];
            }, $todoList['jobDetails']['tasks']);
            echo json_encode($tasks);
        } else {
            echo json_encode([]);
        }

        $stmt->close();
        $conn->close();
        exit;

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => $e->getMessage()]);
        exit;
    }
}

// معالجة طلب AJAX للحصول على بيانات العقد
if (isset($_GET['action']) && $_GET['action'] === 'get_contract_data') {
    header('Content-Type: application/json');
    try {
        if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
            throw new Exception('Invalid contract ID');
        }

        $contractId = intval($_GET['id']);

        // قراءة تفاصيل الاتصال بقاعدة البيانات
        $file = fopen(__DIR__ . "/connection/one.txt", "r");
        if (!$file) {
            throw new Exception('Error reading configuration file');
        }

        $servername = trim(fgets($file));
        $username = trim(fgets($file));
        $password = trim(fgets($file));
        $dbname = trim(fgets($file));
        fclose($file);

        $conn = new mysqli($servername, $username, $password, $dbname);
        $conn->set_charset("utf8");

        if ($conn->connect_error) {
            throw new Exception("Database connection failed: " . $conn->connect_error);
        }

        // الحصول على بيانات العقد
        $stmt = $conn->prepare("SELECT c.*, e.name_ar_contract, e.name_en_contract 
                              FROM contract c 
                              LEFT JOIN employees e ON c.id_employees = e.id_employees 
                              WHERE c.id_contract = ?");
        if (!$stmt) {
            throw new Exception("Query preparation failed: " . $conn->error);
        }

        $stmt->bind_param("i", $contractId);
        if (!$stmt->execute()) {
            throw new Exception("Query execution failed: " . $stmt->error);
        }

        $result = $stmt->get_result();
        if ($row = $result->fetch_assoc()) {
            // تنسيق التواريخ لتتوافق مع حقول الإدخال في HTML (YYYY-MM-DD)
            if ($row['version_date']) {
                $date = new DateTime($row['version_date']);
                $row['version_date'] = $date->format('Y-m-d');
            }

            if ($row['start_date_contract']) {
                $date = new DateTime($row['start_date_contract']);
                $row['start_date_contract'] = $date->format('Y-m-d');
            }

            // تحويل تاريخ النهاية لكي يكون متوافق مع الواجهة (null إذا كان العقد مفتوح)
            if ($row['end_date_contract'] === null) {
                $row['end_date_contract'] = '';
            } else {
                $date = new DateTime($row['end_date_contract']);
                $row['end_date_contract'] = $date->format('Y-m-d');
            }

            // تحديد نوع العقد (1-4)
            if ($row['contract_type'] == 1) { // شهري
                if ($row['end_date_contract']) {
                    $row['contract_type'] = 2; // شهري مغلق
                } else {
                    $row['contract_type'] = 1; // شهري مفتوح
                }
            } else { // يومي
                if ($row['end_date_contract']) {
                    $row['contract_type'] = 4; // يومي مغلق
                } else {
                    $row['contract_type'] = 3; // يومي مفتوح
                }
            }
            
            echo json_encode($row);
        } else {
            http_response_code(404);
            echo json_encode(['error' => 'Contract not found']);
        }

        $stmt->close();
        $conn->close();
        exit;

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => $e->getMessage()]);
        exit;
    }
}

// معالجة طلب AJAX للحصول على قائمة العقود
if (isset($_GET['action']) && $_GET['action'] === 'get_contracts') {
    header('Content-Type: application/json');
    try {
        // قراءة تفاصيل الاتصال بقاعدة البيانات
        $file = fopen(__DIR__ . "/connection/one.txt", "r");
        if (!$file) {
            throw new Exception('Error reading configuration file');
        }

        $servername = trim(fgets($file));
        $username = trim(fgets($file));
        $password = trim(fgets($file));
        $dbname = trim(fgets($file));
        fclose($file);

        // إنشاء اتصال بقاعدة البيانات
        $conn = new mysqli($servername, $username, $password, $dbname);
        $conn->set_charset("utf8");

        if ($conn->connect_error) {
            throw new Exception("Database connection failed: " . $conn->connect_error);
        }

        // تحديد المعايير للبحث والتصفية
        $search = isset($_GET['search']) ? $_GET['search'] : '';
        $projectFilter = isset($_GET['project_id']) ? intval($_GET['project_id']) : 0;
        
        // بناء استعلام SQL مع معايير البحث والتصفية
                    $sql = "SELECT 
                c.id_contract, 
                e.name_ar_contract, 
                e.name_en_contract,
                p.Project_name,
                c.id_Project,
                CASE 
                    WHEN c.contract_type = 1 THEN 'شهري مفتوح'
                    WHEN c.contract_type = 2 THEN 'شهري مغلق'
                    WHEN c.contract_type = 3 THEN 'يومي مفتوح'
                    WHEN c.contract_type = 4 THEN 'يومي مغلق'
                    ELSE 'غير محدد'
                END as contract_type_name,
                c.add_contract
            FROM contract c
            LEFT JOIN employees e ON c.id_employees = e.id_employees
            LEFT JOIN project p ON c.id_Project = p.id_Project
            WHERE c.status_contract != 0";
        
        // إضافة شرط البحث
        if (!empty($search)) {
            $search = $conn->real_escape_string($search);
            $sql .= " AND (e.name_ar_contract LIKE '%$search%' OR 
                         e.name_en_contract LIKE '%$search%' OR 
                         c.id_contract LIKE '%$search%' OR
                         p.Project_name LIKE '%$search%')";
        }
        
        // إضافة شرط تصفية المشروع
        if ($projectFilter > 0) {
            $sql .= " AND c.id_Project = $projectFilter";
        }
        
        // ترتيب النتائج
        $sql .= " ORDER BY c.id_contract DESC";
        
        $result = $conn->query($sql);
        if (!$result) {
            throw new Exception("Query execution failed: " . $conn->error);
        }
        
        $contracts = [];
        while ($row = $result->fetch_assoc()) {
            // تنسيق التاريخ
            $date = new DateTime($row['add_contract']);
            $row['add_contract'] = $date->format('Y-m-d');
            
            $contracts[] = $row;
        }
        
        echo json_encode(['data' => $contracts]);
        $conn->close();
        exit;
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => $e->getMessage()]);
        exit;
    }
}

$error_message = '';
$success_message = '';

// جلب المشاريع والمسميات الوظيفية من قاعدة البيانات
try {
    $file = fopen(__DIR__ . "/connection/one.txt", "r");
    if (!$file) {
        throw new Exception('خطأ في قراءة ملف الإعدادات');
    }

    $servername = trim(fgets($file));
    $username = trim(fgets($file));
    $password = trim(fgets($file));
    $dbname = trim(fgets($file));
    fclose($file);

    $conn = new mysqli($servername, $username, $password, $dbname);
    $conn->set_charset("utf8");

    if ($conn->connect_error) {
        throw new Exception("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
    }

    // جلب المشاريع
    $projects = [];
    $result = $conn->query("SELECT id_Project, Project_name FROM Project WHERE Project_status = 1");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $projects[] = $row;
        }
    }

    // جلب الموظفين
    $employees = [];
    $result = $conn->query("SELECT id_employees, name_ar_contract, name_en_contract FROM employees WHERE status = 1");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $employees[] = $row;
        }
    }

    // جلب المسميات الوظيفية مع عدد المهام
    $job_titles = [];
    $result = $conn->query("SELECT id_Job_titles, name_Job, name_Job_en, data_todo_list_Job FROM Job_titles");

    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $job_titles[] = [
                'id' => $row['id_Job_titles'],
                'name' => $row['name_Job'],
                'name_en' => $row['name_Job_en'],
                'data' => $row['data_todo_list_Job']
            ];
        }
    }
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // التحقق من صحة البيانات وتجهيزها
        $contract_id = isset($_POST['contract_id']) ? intval($_POST['contract_id']) : 0;
        $is_update = $contract_id > 0;
        
        $project_id = intval($_POST['project_id'] ?? 0);
        $employee_ids = $_POST['employee_id'] ?? [];
        $selected_contract_type = intval($_POST['contract_type'] ?? 0);
        
        // تحويل نوع العقد إلى القيمة المناسبة في قاعدة البيانات
        $contract_type = ($selected_contract_type <= 2) ? 1 : 2;

        $version_date = trim($_POST['version_date'] ?? '');
        $start_date_contract = trim($_POST['start_date_contract'] ?? '');
        $end_date_contract = trim($_POST['end_date_contract'] ?? '');
        $wage_contract = trim($_POST['wage_contract'] ?? '');
        $job_title_id = trim($_POST['job_title_id'] ?? '');
        $data_todo_list_contract = trim($_POST['json_output'] ?? '');
        $amount_written_ar = trim($_POST['amount_written_ar'] ?? '');
        
        // Special handling for amount_written_en to ensure it's captured correctly
        $amount_written_en = '';
        
        // Try multiple sources for the amount_written_en value in order of preference
        if (isset($_POST['amount_written_en_hidden']) && !empty($_POST['amount_written_en_hidden'])) {
            // First try the hidden field
            $amount_written_en = trim($_POST['amount_written_en_hidden']);
            error_log("Using hidden field for amount_written_en: " . $amount_written_en);
        } elseif (isset($_POST['amount_written_en_backup']) && !empty($_POST['amount_written_en_backup'])) {
            // Then try the backup field
            $amount_written_en = trim($_POST['amount_written_en_backup']);
            error_log("Using backup field for amount_written_en: " . $amount_written_en);
        } elseif (isset($_POST['amount_written_en'])) {
            // Finally try the standard field
            $amount_written_en = trim($_POST['amount_written_en']);
            error_log("Using standard field for amount_written_en: " . $amount_written_en);
        }
        
        // Log the raw values for debugging
        error_log("Raw amount_written_en from POST: " . ($_POST['amount_written_en'] ?? 'NOT SET'));
        error_log("Raw amount_written_en_backup from POST: " . ($_POST['amount_written_en_backup'] ?? 'NOT SET'));
        error_log("Raw amount_written_en_hidden from POST: " . ($_POST['amount_written_en_hidden'] ?? 'NOT SET'));

        $validation_errors = [];

        // التحقق من المشروع
        if (empty($project_id)) {
            $validation_errors[] = 'يرجى اختيار مشروع';
        }

        // التحقق من الموظفين - تجاهل التحقق إذا كان تحديثًا
        if (!$is_update && (empty($employee_ids) || !is_array($employee_ids))) {
            $validation_errors[] = 'يرجى اختيار موظف واحد على الأقل';
        }

        // التحقق من نوع العقد
        if (empty($selected_contract_type) || !in_array($selected_contract_type, [1, 2, 3, 4])) {
            $validation_errors[] = 'يرجى اختيار نوع العقد';
        }

        // التحقق من تاريخ الإصدار
        if (empty($version_date)) {
            $validation_errors[] = 'يرجى إدخال تاريخ الإصدار';
        }

        // التحقق من تاريخ البداية
        if (empty($start_date_contract)) {
            $validation_errors[] = 'يرجى إدخال تاريخ البداية';
        }

        // التحقق من تاريخ النهاية بناءً على نوع العقد
        if ($selected_contract_type === 2 || $selected_contract_type === 4) { // Closed Monthly or Daily
            if (empty($end_date_contract)) {
                $validation_errors[] = 'يرجى إدخال تاريخ النهاية للعقد المغلق';
            } else {
                $start = new DateTime($start_date_contract);
                $end = new DateTime($end_date_contract);
                $interval = $start->diff($end);
                
                if ($end <= $start) {
                    $validation_errors[] = 'تاريخ النهاية يجب أن يكون بعد تاريخ البداية';
                }
                
                if ($selected_contract_type === 2) { // Closed Monthly
                    // حساب الأشهر الكاملة
                    $months = ($interval->y * 12) + $interval->m;
                    if ($interval->d > 0) $months++; // إذا كان هناك أيام إضافية، فهذا ليس شهرًا كاملاً
                    
                    if ($months < 1) {
                        $validation_errors[] = 'يجب أن تكون مدة العقد شهراً واحداً على الأقل للعقود الشهرية';
                    }
                } elseif ($selected_contract_type === 4) { // Closed Daily
                    $days = $interval->days;
                    if ($days < 1) {
                        $validation_errors[] = 'يجب أن تكون مدة العقد يوماً واحداً على الأقل للعقود اليومية';
                    }
                }
            }
        }

        // التحقق من الراتب
        if (empty($wage_contract)) {
            $validation_errors[] = 'يرجى إدخال الراتب';
        }

        // التحقق من المسمى الوظيفي
        if (empty($job_title_id)) {
            $validation_errors[] = 'يرجى اختيار مسمى وظيفي';
        }

        // التحقق من صحة JSON
        if (empty($data_todo_list_contract)) {
            $validation_errors[] = 'يرجى إدخال بيانات JSON للمهام';
        } else {
            $decoded_json = json_decode($data_todo_list_contract, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                $validation_errors[] = 'بيانات JSON غير صالحة: ' . json_last_error_msg();
            } elseif (!isset($decoded_json['jobDetails']) || !isset($decoded_json['jobDetails']['tasks']) || empty($decoded_json['jobDetails']['tasks'])) {
                $validation_errors[] = 'يجب إضافة مهمة واحدة على الأقل';
            }
        }

        // التحقق من المبلغ المكتوب
        if (empty($amount_written_ar)) {
            $validation_errors[] = 'المبلغ بالعربية مطلوب';
        }

        if (empty($amount_written_en)) {
            $validation_errors[] = 'المبلغ بالإنجليزية مطلوب';
        }

        // التحقق من نوع العملة
        if (empty($_POST['currency_type']) || !in_array($_POST['currency_type'], ['1', '2'])) {
            $validation_errors[] = 'يرجى اختيار نوع العملة';
        }

        if (empty($validation_errors)) {
            // إعداد اتصال قاعدة البيانات
            try {
                if ($is_update) {
                    // First, get the current contract type to check if it's changing
                    $current_contract_sql = "SELECT contract_type, end_date_contract FROM contract WHERE id_contract = ?";
                    $current_stmt = $conn->prepare($current_contract_sql);
                    if (!$current_stmt) {
                        throw new Exception("خطأ في إعداد استعلام العقد الحالي: " . $conn->error);
                    }

                    $current_stmt->bind_param("i", $contract_id);
                    $current_stmt->execute();
                    $current_result = $current_stmt->get_result();
                    $current_contract = $current_result->fetch_assoc();
                    $current_stmt->close();

                    if (!$current_contract) {
                        throw new Exception("العقد المطلوب تحديثه غير موجود");
                    }

                    $current_contract_type = $current_contract['contract_type'];

                    // Determine current contract type in 4-value system (1-4)
                    $current_selected_contract_type = 0;
                    if ($current_contract_type == 1) { // Monthly
                        if ($current_contract['end_date_contract']) {
                            $current_selected_contract_type = 2; // Monthly Closed
                        } else {
                            $current_selected_contract_type = 1; // Monthly Open
                        }
                    } else { // Daily
                        if ($current_contract['end_date_contract']) {
                            $current_selected_contract_type = 4; // Daily Closed
                        } else {
                            $current_selected_contract_type = 3; // Daily Open
                        }
                    }

                    // Check if contract type is changing
                    $contract_type_changed = ($current_contract_type != $contract_type);
                    $achievement_reports_deleted = false;
                    $permanent_diapers_deleted = false;

                    if ($contract_type_changed) {
                        // Contract type is changing, delete all related achievement_reports
                        $delete_reports_sql = "DELETE FROM achievement_reports WHERE id_contract = ?";
                        $delete_stmt = $conn->prepare($delete_reports_sql);
                        if (!$delete_stmt) {
                            throw new Exception("خطأ في إعداد استعلام حذف تقارير الإنجاز: " . $conn->error);
                        }

                        $delete_stmt->bind_param("i", $contract_id);
                        if (!$delete_stmt->execute()) {
                            throw new Exception("فشل في حذف تقارير الإنجاز المرتبطة بالعقد: " . $delete_stmt->error);
                        }
                        $delete_stmt->close();
                        $achievement_reports_deleted = true;

                        // Log the deletion for debugging
                        error_log("Contract type changed from $current_contract_type to $contract_type for contract ID $contract_id. Deleted related achievement_reports.");
                    }

                    // Check if there's a change between open/closed states
                    // Open states: 1 (Monthly Open), 3 (Daily Open)
                    // Closed states: 2 (Monthly Closed), 4 (Daily Closed)
                    $current_is_open = ($current_selected_contract_type == 1 || $current_selected_contract_type == 3);
                    $new_is_open = ($selected_contract_type == 1 || $selected_contract_type == 3);

                    if ($current_is_open != $new_is_open) {
                        // Open/Closed state is changing, delete all related permanent_diapers (attendance records)
                        $delete_diapers_sql = "DELETE FROM permanent_diapers WHERE id_contract = ?";
                        $delete_diapers_stmt = $conn->prepare($delete_diapers_sql);
                        if (!$delete_diapers_stmt) {
                            throw new Exception("خطأ في إعداد استعلام حذف سجلات الحضور: " . $conn->error);
                        }

                        $delete_diapers_stmt->bind_param("i", $contract_id);
                        if (!$delete_diapers_stmt->execute()) {
                            throw new Exception("فشل في حذف سجلات الحضور المرتبطة بالعقد: " . $delete_diapers_stmt->error);
                        }
                        $delete_diapers_stmt->close();
                        $permanent_diapers_deleted = true;

                        // Log the deletion for debugging
                        error_log("Contract open/closed state changed from " . ($current_is_open ? "open" : "closed") . " to " . ($new_is_open ? "open" : "closed") . " for contract ID $contract_id. Deleted related permanent_diapers.");
                    }

                    // تحديث عقد موجود
                    $sql = "UPDATE contract SET
                            id_Project = ?,
                            contract_type = ?,
                            version_date = ?,
                            start_date_contract = ?,
                            end_date_contract = ?,
                            wage_contract = ?,
                            data_todo_list_contract = ?,
                            name_Job = ?,
                            name_Job_en = ?,
                            amount_written_ar = ?,
                            amount_written_en = ?,
                            Type_currency = ?
                        WHERE id_contract = ?";

                    $stmt = $conn->prepare($sql);
                    if (!$stmt) {
                        throw new Exception("خطأ في إعداد الاستعلام: " . $conn->error);
                    }

                    // Get both Arabic and English job names from the list
                    $job_name = '';
                    $job_name_en = '';
                    foreach ($job_titles as $job) {
                        if ($job['id'] == $job_title_id) {
                            $job_name = $job['name'];
                            $job_name_en = $job['name_en'] ?? ''; // Use empty string if name_en is not set
                            break;
                        }
                    }

                    // معالجة تاريخ الانتهاء (NULL إذا كان فارغاً)
                    $end_date = empty($end_date_contract) ? null : $end_date_contract;

                    // Debug info
                    error_log("Parameters for update: project_id=$project_id, contract_type=$contract_type, version_date=$version_date, start_date=$start_date_contract, end_date=" . ($end_date ?? 'NULL') . ", wage=$wage_contract, job_name=$job_name, job_name_en=$job_name_en, amount_ar=$amount_written_ar, amount_en=$amount_written_en, currency=" . $_POST['currency_type'] . ", contract_id=$contract_id");
                    
                    // Additional debug for amount_written_en
                    error_log("POST data for amount_written_en: " . (isset($_POST['amount_written_en']) ? $_POST['amount_written_en'] : 'NOT SET'));
                    
                    // Make sure amount_written_en is not empty
                    if (empty($amount_written_en)) {
                        error_log("Warning: amount_written_en is empty, setting default value");
                        $amount_written_en = "Not specified";
                    }
                    
                    // Print all values for debugging
                    error_log("DEBUG VALUES FOR UPDATE:");
                    error_log("project_id: $project_id");
                    error_log("contract_type: $contract_type");
                    error_log("version_date: $version_date");
                    error_log("start_date_contract: $start_date_contract");
                    error_log("end_date: " . ($end_date ?? 'NULL'));
                    error_log("wage_contract: $wage_contract");
                    error_log("data_todo_list_contract length: " . strlen($data_todo_list_contract));
                    error_log("job_name: $job_name");
                    error_log("job_name_en: $job_name_en");
                    error_log("amount_written_ar: $amount_written_ar");
                    error_log("amount_written_en: $amount_written_en");
                    error_log("currency_type: " . $_POST['currency_type']);
                    error_log("contract_id: $contract_id");
                    
                    // The issue might be with the parameter types - change to all strings for text fields
                    $stmt->bind_param("iisssssssssii",
                        $project_id,           // i (id_Project)
                        $contract_type,        // i (contract_type)
                        $version_date,         // s (version_date)
                        $start_date_contract,  // s (start_date_contract)
                        $end_date,             // s (end_date_contract)
                        $wage_contract,        // s (wage_contract as string)
                        $data_todo_list_contract, // s (data_todo_list_contract)
                        $job_name,             // s (name_Job)
                        $job_name_en,          // s (name_Job_en)
                        $amount_written_ar,    // s (amount_written_ar)
                        $amount_written_en,    // s (amount_written_en)
                        $_POST['currency_type'], // i (Type_currency)
                        $contract_id           // i (id_contract WHERE clause)
                    );

                    if ($stmt->execute()) {
                        // Build success message based on what was deleted
                        $success_message = "تم تحديث العقد بنجاح";

                        if ($achievement_reports_deleted && $permanent_diapers_deleted) {
                            $success_message .= ". تم حذف تقارير الإنجاز وسجلات الحضور المرتبطة بالعقد بسبب تغيير نوع العقد.";
                        } elseif ($achievement_reports_deleted) {
                            $success_message .= ". تم حذف تقارير الإنجاز المرتبطة بالعقد بسبب تغيير نوع العقد.";
                        } elseif ($permanent_diapers_deleted) {
                            $success_message .= ". تم حذف سجلات الحضور المرتبطة بالعقد بسبب تغيير حالة العقد من مفتوح إلى مغلق أو العكس.";
                        }
                    } else {
                        $error_message = "فشل في تحديث العقد: " . $stmt->error;
                    }
                } else {
                    // إضافة العقود إلى قاعدة البيانات (الكود الحالي)
                    $sql = "INSERT INTO contract (
                        id_Project,
                        id_employees, 
                        contract_type,
                        version_date,
                        start_date_contract,
                        end_date_contract,
                        wage_contract,
                        data_todo_list_contract,
                        name_Job,
                        name_Job_en,
                        amount_written_ar,
                        amount_written_en,
                        Type_currency
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                    
                    $stmt = $conn->prepare($sql);
                    if (!$stmt) {
                        throw new Exception("خطأ في إعداد الاستعلام: " . $conn->error);
                    }

                    // Get both Arabic and English job names from the list
                    $job_name = '';
                    $job_name_en = '';
                    foreach ($job_titles as $job) {
                        if ($job['id'] == $job_title_id) {
                            $job_name = $job['name'];
                            $job_name_en = $job['name_en'];
                            break;
                        }
                    }

                    if (empty($job_name) || empty($job_name_en)) {
                        throw new Exception("لم يتم العثور على المسمى الوظيفي المحدد");
                    }

                    // معالجة تاريخ الانتهاء (NULL إذا كان فارغاً)
                    $end_date = empty($end_date_contract) ? null : $end_date_contract;
                    
                    // Make sure amount_written_en is not empty
                    if (empty($amount_written_en)) {
                        error_log("Warning: amount_written_en is empty in INSERT, setting default value");
                        $amount_written_en = "Not specified";
                    }

                    $success_count = 0;
                    $error_count = 0;

                    foreach ($employee_ids as $employee_id) {
                        $stmt->bind_param("iiisssdsssssi",
                            $project_id,
                            $employee_id,
                            $contract_type,
                            $version_date,
                            $start_date_contract,
                            $end_date,
                            $wage_contract,
                            $data_todo_list_contract,
                            $job_name,
                            $job_name_en,
                            $amount_written_ar,
                            $amount_written_en,
                            $_POST['currency_type']
                        );

                        if ($stmt->execute()) {
                            $success_count++;
                        } else {
                            $error_count++;
                        }
                    }

                    if ($success_count > 0) {
                        $success_message = "تم إنشاء {$success_count} عقد بنجاح";
                        if ($error_count > 0) {
                            $success_message .= " (فشل إنشاء {$error_count} عقد)";
                        }
                    } else {
                        $error_message = "فشل في إنشاء العقود";
                    }
                }
            } catch (Exception $e) {
                $error_message = "خطأ في قاعدة البيانات: " . $e->getMessage();
            }
        } else {
            $error_message = "أخطاء التحقق:<br>" . implode("<br>", $validation_errors);
        }
    }
} catch (Exception $e) {
    $error_message = "خطأ في قاعدة البيانات: " . $e->getMessage();
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء عقد جديد - نظام إدارة الموارد البشرية</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet">
    <link href="../assets/lib/datatables/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <link href="../assets/css/sidebar.css" rel="stylesheet">
    
    <style>
        /* Alert styling */
        .alert-info {
            margin: -0.5rem;
            border-radius: 0 0 8px 8px;
            border-left: none;
            border-right: none;
            border-bottom: none;
        }

        /* Custom styling for Select2 dropdown items */
        .select2-results__option {
            white-space: nowrap !important;
            overflow-x: auto !important;
            padding: 8px !important;
        }

        .select2-results__option::-webkit-scrollbar {
            height: 4px;
        }

        .select2-results__option::-webkit-scrollbar-track {
            background: var(--bg-input);
            border-radius: 2px;
        }

        .select2-results__option::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 2px;
        }

        /* Contract info in Select2 */
        .contract-info {
            display: inline-block;
            padding: 4px 8px;
            margin-right: 8px;
            border-radius: 4px;
            background: rgba(13, 110, 253, 0.1);
            white-space: nowrap;
        }

        [data-theme="dark"] .contract-info {
            background: rgba(255, 255, 255, 0.1);
        }

        /* Dark theme support */
        [data-theme="dark"] .alert-info {
            background-color: var(--dark-info-bg);
            color: var(--dark-info-text);
            border-color: var(--dark-info-border);
        }

        /* Select2 Dark Theme Support */
        [data-theme="dark"] .select2-container--default .select2-selection--multiple {
            background-color: var(--dark-bg);
            border-color: var(--dark-border);
            color: var(--dark-text);
        }

        [data-theme="dark"] .select2-container--default .select2-selection--multiple .select2-selection__choice {
            background-color: var(--dark-primary);
            border-color: var(--dark-primary);
            color: var(--dark-text);
        }

        [data-theme="dark"] .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
            color: var(--dark-text);
        }

        [data-theme="dark"] .select2-container--default .select2-results__option {
            background-color: #1a1d20 !important;
            color: #ffffff !important;
        }

        [data-theme="dark"] .select2-container--default .select2-results__option--highlighted[aria-selected] {
            background-color: #2c3034 !important;
            color: #ffffff !important;
        }

        [data-theme="dark"] .select2-dropdown {
            background-color: #1a1d20 !important;
            border-color: #2c3034 !important;
        }

        /* Additional Dark Theme Fixes for Select2 Dropdown */
        [data-theme="dark"] .select2-container--default .select2-search--dropdown .select2-search__field {
            background-color: #1a1d20 !important;
            color: #ffffff !important;
            border-color: #2c3034 !important;
        }

        [data-theme="dark"] .select2-container--default .select2-results__option[aria-selected=true] {
            background-color: #2c3034 !important;
            color: #ffffff !important;
        }

        [data-theme="dark"] .select2-container--default .select2-results__option--highlighted[aria-selected] {
            background-color: #2c3034 !important;
            color: #ffffff !important;
        }

        [data-theme="dark"] .select2-container--default .select2-results__group {
            background-color: #1a1d20 !important;
            color: #ffffff !important;
        }

        [data-theme="dark"] .select2-container--default .select2-results__option[aria-disabled=true] {
            color: rgba(255, 255, 255, 0.5) !important;
        }

        [data-theme="dark"] .select2-container--default .select2-results__option--highlighted[aria-disabled=true] {
            background-color: #1a1d20 !important;
            color: rgba(255, 255, 255, 0.5) !important;
        }

        /* Select2 Field Size and Scrolling */
        .select2-container {
            width: 100% !important;
        }

        /* Section styling */
        .section-container {
            border: 1px solid var(--border-color);
            border-radius: 0.375rem;
            background-color: #f8f9fa;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        [data-theme="dark"] .section-container {
            background-color: rgba(255, 255, 255, 0.05);
        }

        .section-title {
            color: #0d6efd;
            font-weight: 600;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #0d6efd;
        }

        /* Form controls */
        .form-control, .form-select {
            background-color: var(--bg-input);
            border-color: var(--input-border);
            color: var(--text-color);
        }

        .form-control:focus, .form-select:focus {
            background-color: var(--bg-input);
            border-color: var(--input-focus-border);
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
            color: var(--text-color);
        }

        .form-control:disabled {
            background-color: var(--hover-color);
            color: var(--text-muted);
        }

        /* Helper text styling */
        .text-muted {
            color: #6c757d !important;
        }

        [data-theme="dark"] .text-muted {
            color: rgba(255, 255, 255, 0.75) !important;
        }

        /* Primary color overrides */
        .text-primary {
            color: #0d6efd !important;
        }

        .btn-primary {
            background-color: #0d6efd;
            border-color: #0d6efd;
        }

        .btn-primary:hover {
            background-color: #0b5ed7;
            border-color: #0a58ca;
        }

        /* Save button styling */
        .save-contract-btn {
            background: linear-gradient(45deg, #0d6efd, #0a58ca);
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            font-weight: 600;
            min-width: 200px;
        }

        .save-contract-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
            background: linear-gradient(45deg, #0a58ca, #0d6efd);
        }

        .save-contract-btn:active {
            transform: translateY(1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* Select2 styling */
        .select2-container--default .select2-selection--single {
            background-color: var(--bg-input);
            border-color: var(--input-border);
            color: var(--text-color);
            height: 38px;
            line-height: 38px;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 38px;
            color: var(--text-color);
        }

        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: 36px;
        }

        .select2-dropdown {
            background-color: var(--bg-input);
            border-color: var(--input-border);
        }

        .select2-container--default .select2-results__option {
            color: var(--text-color);
        }

        .select2-container--default .select2-results__option--highlighted[aria-selected] {
            background-color: var(--primary-color);
        }

        /* Task list styling */
        .task-list-container {
            border: 1px solid var(--border-color);
            border-radius: 0.375rem;
            background-color: var(--bg-input);
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .task-list-scrollable {
            max-height: 400px;
            overflow-y: auto;
            padding-right: 0.5rem;
        }

        .task-list-scrollable::-webkit-scrollbar {
            width: 8px;
        }

        .task-list-scrollable::-webkit-scrollbar-track {
            background: var(--bg-input);
            border-radius: 4px;
        }

        .task-list-scrollable::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 4px;
        }

        .task-list-scrollable::-webkit-scrollbar-thumb:hover {
            background: var(--secondary-color);
        }

        .task-item {
            background-color: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
        }

        .task-item:hover {
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .task-item:last-child {
            margin-bottom: 0;
        }

        .task-input-container {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            flex: 1;
        }

        .task-actions {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }

        /* Delete button styling */
        .btn-danger.btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
            line-height: 1.5;
            border-radius: 0.2rem;
        }

        .btn-danger.btn-sm:hover {
            opacity: 0.9;
        }

        .text-end {
            text-align: end;
            margin-top: -0.5rem; /* Adjust spacing */
        }

        /* Card styling */
        .card {
            background-color: var(--bg-card);
            border-color: var(--border-color);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .card-title {
            color: var(--text-color);
            font-weight: 600;
            margin-bottom: 1.5rem;
            padding-bottom: 0.75rem;
            border-bottom: 2px solid var(--primary-color);
        }

        /* Button styling */
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: var(--btn-text);
        }

        .btn-primary:hover {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        .btn-danger {
            color: var(--btn-text);
        }

        /* Duration helper text */
        .duration-helper {
            font-size: 0.875rem;
            color: var(--text-muted);
            margin-top: 0.25rem;
        }

        /* JSON section styling */
        .btn-secondary {
            background-color: var(--bg-card);
            border-color: var(--border-color);
            color: var(--text-color);
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background-color: var(--hover-color);
            border-color: var(--border-color);
            color: var(--text-color);
        }

        .btn-secondary .bi-chevron-down {
            transition: transform 0.3s ease;
        }

        .btn-secondary[aria-expanded="true"] .bi-chevron-down {
            transform: rotate(180deg);
        }

        #jsonSection {
            transition: all 0.3s ease;
        }

        #jsonSection.collapsing {
            transition: all 0.3s ease;
        }

        #jsonSection.show {
            margin-top: 1rem;
        }

        #json_output {
            background-color: var(--bg-input);
            color: var(--text-color);
            font-family: monospace;
            font-size: 0.875rem;
            resize: vertical;
        }

        /* Identity attachment styling */
        .identity-attachment-container {
            transition: all 0.3s ease;
        }

        .identity-attachment-container:hover {
            background-color: var(--bg-input) !important;
        }

        .border-dashed {
            border-style: dashed !important;
        }

        .upload-box {
            transition: all 0.3s ease;
        }

        .upload-box:hover {
            border-color: var(--primary-color) !important;
            background-color: rgba(var(--primary-rgb), 0.05);
        }

        /* Dark theme support */
        [data-theme="dark"] .identity-attachment-container {
            background-color: rgba(255, 255, 255, 0.05) !important;
        }

        [data-theme="dark"] .upload-box:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        [data-theme="dark"] .selected-file {
            color: rgba(255, 255, 255, 0.7) !important;
        }

        [data-theme="dark"] .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
            color: var(--dark-text);
        }

        [data-theme="dark"] .select2-container--default .select2-results__option {
            background-color: #1a1d20 !important;
            color: #ffffff !important;
        }

        [data-theme="dark"] .select2-container--default .select2-results__option--highlighted[aria-selected] {
            background-color: #2c3034 !important;
            color: #ffffff !important;
        }

        [data-theme="dark"] .select2-dropdown {
            background-color: #1a1d20 !important;
            border-color: #2c3034 !important;
        }

        /* Additional Dark Theme Fixes for Select2 Dropdown */
        [data-theme="dark"] .select2-container--default .select2-search--dropdown .select2-search__field {
            background-color: #1a1d20 !important;
            color: #ffffff !important;
            border-color: #2c3034 !important;
        }

        [data-theme="dark"] .select2-container--default .select2-results__option[aria-selected=true] {
            background-color: #2c3034 !important;
            color: #ffffff !important;
        }

        [data-theme="dark"] .select2-container--default .select2-results__option--highlighted[aria-selected] {
            background-color: #2c3034 !important;
            color: #ffffff !important;
        }

        [data-theme="dark"] .select2-container--default .select2-results__group {
            background-color: #1a1d20 !important;
            color: #ffffff !important;
        }

        [data-theme="dark"] .select2-container--default .select2-results__option[aria-disabled=true] {
            color: rgba(255, 255, 255, 0.5) !important;
        }

        [data-theme="dark"] .select2-container--default .select2-results__option--highlighted[aria-disabled=true] {
            background-color: #1a1d20 !important;
            color: rgba(255, 255, 255, 0.5) !important;
        }

        /* Contract Management Table Styling */
        .contract-table-container {
            max-height: 400px;
            overflow-y: auto;
            margin-bottom: 1.5rem;
            border: 1px solid var(--border-color);
            border-radius: 0.375rem;
        }

        [data-theme="dark"] .contract-table-container {
            border-color: var(--dark-border);
        }

        #contracts-table {
            margin-bottom: 0;
            border-collapse: collapse;
        }

        #contracts-table thead th {
            position: sticky;
            top: 0;
            background-color: var(--bg-card);
            z-index: 10;
            border: 1px solid var(--border-color);
            border-bottom: 2px solid var(--border-color);
            font-weight: 600;
            color: var(--primary-color);
            text-align: center; /* Center align headers */
            vertical-align: middle;
        }

        [data-theme="dark"] #contracts-table thead th {
            background-color: var(--dark-bg);
            border-color: var(--dark-border);
            color: var(--dark-primary);
        }

        [data-theme="dark"] #contracts-table tbody td {
            border-color: var(--dark-border);
        }

        #contracts-table tbody tr {
            cursor: pointer;
            transition: all 0.2s ease;
        }

        #contracts-table tbody tr:hover {
            background-color: rgba(13, 110, 253, 0.1);
        }

        [data-theme="dark"] #contracts-table tbody tr:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        /* Custom scrollbar for the contract table */
        .contract-table-container::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        .contract-table-container::-webkit-scrollbar-track {
            background: var(--bg-input);
            border-radius: 4px;
        }

        .contract-table-container::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 4px;
        }

        .contract-table-container::-webkit-scrollbar-thumb:hover {
            background: var(--secondary-color);
        }

        [data-theme="dark"] .contract-table-container::-webkit-scrollbar-track {
            background: var(--dark-bg);
        }

        [data-theme="dark"] .contract-table-container::-webkit-scrollbar-thumb {
            background: var(--dark-primary);
        }

        /* Select2 Field Size and Scrolling */
        #contracts-table tbody td {
            border: 1px solid var(--border-color); /* Add borders to cells */
            vertical-align: middle;
            padding: 0.5rem;
        }
    </style>
</head>
<body data-theme="light">
    <?php
    // Include the sidebar
    include 'sidebar.php';
    ?>

    <main id="content">
        <div class="container-fluid py-4">
            <div class="row">
                <div class="col-12">
                    <?php if (!empty($error_message)): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo $error_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($success_message)): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo $success_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>
                    
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">إنشاء عقد جديد</h5>
                            
                            <!-- Alert Info -->
                            <div class="alert alert-info mb-4" role="alert">
                                <i class="bi bi-info-circle me-2"></i>
                                مرحباً بك في صفحة إنشاء العقود. يمكنك من خلال هذه الصفحة إنشاء عقود جديدة وتحديد تفاصيلها وربطها بالمشاريع والمسميات الوظيفية.
                                يمكنك إدارة العقود الموجودة من خلال <a href="counter_essential_set.php?table=contracts" class="alert-link">صفحة إدارة العقود</a>.
                            </div>
                            
                            <!-- Contract Selection Section -->
                            <div class="section-container mb-4" id="contract-selection-section">
                                <h6 class="section-title">تحديث عقد موجود</h6>
                                <div class="row">
                                    <div class="col-md-8 mb-3">
                                        <label for="contract_selector" class="form-label">اختر العقد</label>
                                        <select class="form-control select2" id="contract_selector">
                                            <option value="">اختر عقداً للتحديث</option>
                                            <?php
                                            // محاولة استعلام للحصول على جميع العقود مع معلومات إضافية
                                            try {
                                                $query = "SELECT c.id_contract, c.contract_type, e.name_ar_contract, p.Project_name 
                                                         FROM contract c 
                                                         LEFT JOIN employees e ON c.id_employees = e.id_employees 
                                                         LEFT JOIN project p ON c.id_Project = p.id_Project 
                                                         ORDER BY c.id_contract DESC";
                                                $result = $conn->query($query);
                                                
                                                if ($result && $result->num_rows > 0) {
                                                    // تم العثور على عقود، عرضها مع معلومات تفصيلية
                                                    while ($row = $result->fetch_assoc()) {
                                                        // تحديد نوع العقد
                                                        $contractType = '';
                                                        if ($row['contract_type'] == 1) {
                                                            $contractType = 'شهري';
                                                        } else if ($row['contract_type'] == 2) {
                                                            $contractType = 'يومي';
                                                        } else {
                                                            $contractType = 'غير محدد';
                                                        }
                                                        
                                                        // تنسيق النص المعروض في القائمة
                                                        $displayText = sprintf(
                                                            "عقد رقم: %d | %s | %s | %s",
                                                            $row['id_contract'],
                                                            $row['name_ar_contract'] ?: 'غير محدد',
                                                            $row['Project_name'] ?: 'غير محدد',
                                                            $contractType
                                                        );
                                                        
                                                        echo '<option value="' . $row['id_contract'] . '">' . htmlspecialchars($displayText) . '</option>';
                                                    }
                                                } else {
                                                    echo '<option value="">لا توجد عقود متاحة</option>';
                                                }
                                            } catch (Exception $e) {
                                                echo '<option value="">خطأ في تحميل العقود</option>';
                                            }
                                            ?>
                                        </select>
                                    </div>
                                    <div class="col-md-4 mb-3 d-flex align-items-end">
                                        <button type="button" id="viewContractBtn" class="btn btn-info me-2" disabled>
                                            <i class="bi bi-eye-fill"></i> عرض بيانات العقد
                                        </button>
                                        <button type="button" id="resetFormBtn" class="btn btn-secondary">
                                            <i class="bi bi-arrow-repeat"></i> إضافة عقد جديد
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <form id="contractForm" method="post" onsubmit="return validateForm()" enctype="multipart/form-data">
                                <!-- Hidden field for contract ID when updating -->
                                <input type="hidden" id="contract_id" name="contract_id" value="0">
                                <!-- Project and Contract Type Section -->
                                <div class="section-container">
                                    <h6 class="section-title">معلومات المشروع والعقد</h6>
                                    <!-- Project field on its own row -->
                                    <div class="row">
                                        <div class="col-12 mb-3">
                                            <label for="project_id" class="form-label fw-bold mb-2">اختر مشروع</label>
                                            <select class="form-select select2-search" id="project_id" name="project_id" required>
                                                <option value="">اختر المشروع</option>
                                                <?php foreach ($projects as $project): ?>
                                                    <option value="<?php echo htmlspecialchars($project['id_Project']); ?>" <?php echo (isset($_POST['project_id']) && $_POST['project_id'] == $project['id_Project']) ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($project['Project_name']); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>

                                    <!-- Employee and Contract Type fields side by side -->
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="employee_id" class="form-label fw-bold mb-2">اختر موظف</label>
                                            <select class="form-select select2-search" id="employee_id" name="employee_id[]" multiple required>
                                                <?php foreach ($employees as $employee): ?>
                                                    <option value="<?php echo htmlspecialchars($employee['id_employees']); ?>" <?php echo (isset($_POST['employee_id']) && in_array($employee['id_employees'], (array)$_POST['employee_id'])) ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($employee['name_ar_contract']) . ' - ' . htmlspecialchars($employee['name_en_contract']); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                            <small class="text-muted">يمكنك اختيار موظف واحد أو أكثر</small>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="contract_type" class="form-label">نوع العقد</label>
                                            <select class="form-control select2" id="contract_type" name="contract_type" onchange="handleContractTypeChange()">
                                                <option value="">اختر نوع العقد</option>
                                                <option value="1" <?php echo (isset($_POST['contract_type']) && $_POST['contract_type'] == 1) ? 'selected' : ''; ?>>شهري مفتوح</option>
                                                <option value="2" <?php echo (isset($_POST['contract_type']) && $_POST['contract_type'] == 2) ? 'selected' : ''; ?>>شهري مغلق</option>
                                                <option value="3" <?php echo (isset($_POST['contract_type']) && $_POST['contract_type'] == 3) ? 'selected' : ''; ?>>يومي مفتوح</option>
                                                <option value="4" <?php echo (isset($_POST['contract_type']) && $_POST['contract_type'] == 4) ? 'selected' : ''; ?>>يومي مغلق</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <!-- Amount Written Section -->
                                <div class="section-container">
                                    <h6 class="section-title">تفاصيل المستحقات المالية</h6>
                                    <div class="row">
                                        <div class="col-md-3 mb-3">
                                            <label for="currency_type" class="form-label">نوع العملة</label>
                                            <select class="form-select select2" id="currency_type" name="currency_type" required onchange="updateAmountWritten()">
                                                <option value="">اختر نوع العملة</option>
                                                <option value="1">دولار</option>
                                                <option value="2">ريال</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <label for="wage_contract" class="form-label">الراتب</label>
                                            <input type="number" class="form-control" id="wage_contract" name="wage_contract" 
                                                   value="<?php echo isset($_POST['wage_contract']) ? htmlspecialchars($_POST['wage_contract']) : ''; ?>" 
                                                   step="0.01" min="0" required onchange="updateAmountWritten()" oninput="updateAmountWritten()">
                                            <small class="text-muted">سيتم تحويل المبلغ تلقائياً إلى كلمات</small>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <label for="amount_written_ar" class="form-label">المبلغ بالعربية</label>
                                            <input type="text" class="form-control" id="amount_written_ar" name="amount_written_ar" 
                                                   dir="rtl" required>
                                            <small class="text-muted">يمكنك تعديل النص يدوياً</small>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <label for="amount_written_en" class="form-label">المبلغ بالإنجليزية</label>
                                            <input type="text" class="form-control" id="amount_written_en" name="amount_written_en" 
                                                   dir="ltr" required onchange="saveAmountWrittenEn(this.value)" oninput="saveAmountWrittenEn(this.value)">
                                            <small class="text-muted">يمكنك تعديل النص يدوياً</small>
                                            <!-- Hidden field to store the value as backup -->
                                            <input type="hidden" id="amount_written_en_hidden" name="amount_written_en_hidden">
                                        </div>
                                    </div>
                                </div>

                                <!-- Contract Dates Section -->
                                <div class="section-container">
                                    <h6 class="section-title">تواريخ العقد</h6>
                                    <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="version_date" class="form-label">تاريخ إصدار العقد</label>
                                        <input type="date" class="form-control" id="version_date" name="version_date" 
                                               value="<?php echo isset($_POST['version_date']) ? htmlspecialchars($_POST['version_date']) : date('Y-m-d'); ?>" 
                                               required>
                                    </div>
                                    
                                    <div class="col-md-4 mb-3">
                                        <label for="start_date_contract" class="form-label">تاريخ بداية العقد</label>
                                        <input type="date" class="form-control" id="start_date_contract" name="start_date_contract" required onchange="handleStartDateChange()">
                                    </div>
                                    
                                    <div class="col-md-4 mb-3">
                                        <label for="end_date_contract" class="form-label">تاريخ نهاية العقد</label>
                                        <input type="date" class="form-control" id="end_date_contract" name="end_date_contract" onchange="handleEndDateChange()" disabled>
                                    </div>


                                    </div>
                                </div>

                                <!-- Job Title and Tasks Section -->
                                <div class="section-container">
                                    <h6 class="section-title">المسمى الوظيفي والمهام</h6>
                                    <div class="mb-3">
                                        <label for="job_title_id" class="form-label">المسمى الوظيفي</label>
                                        <select class="form-control select2" id="job_title_id" name="job_title_id" onchange="handleJobTitleChange()" disabled>
                                            <option value="">اختر المسمى الوظيفي</option>
                                            <?php foreach ($job_titles as $job): ?>
                                                <option value="<?php echo htmlspecialchars($job['id']); ?>" 
                                                        data-json="<?php echo htmlspecialchars($job['data']); ?>"
                                                        data-name-en="<?php echo htmlspecialchars($job['name_en']); ?>">
                                                    <?php echo htmlspecialchars($job['name']) . ' - ' . htmlspecialchars($job['name_en']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <div id="selected_job_title_display" class="mt-2" style="display: none;">
                                            <small class="text-muted">
                                                <span class="me-2">العربية:</span><span id="job_title_ar" class="text-primary"></span>
                                                <br>
                                                <span class="me-2">English:</span><span id="job_title_en" class="text-primary"></span>
                                            </small>
                                        </div>
                                    </div>

                                    <div class="task-list-container">
                                        <label class="form-label">قائمة المهام</label>
                                        <div id="task_list" class="task-list-scrollable"></div>
                                        <button type="button" id="add_task_btn" class="btn btn-primary mt-3" onclick="addNewTask(event)" disabled>
                                            <i class="bi bi-plus-lg"></i> إضافة مهمة جديدة
                                        </button>
                                    </div>

                                    <!-- Save Contract Button -->
                                    <div class="mt-4 mb-4 text-center">
                                        <button type="submit" class="btn btn-primary btn-lg px-5 py-2 save-contract-btn">
                                            <i class="bi bi-save me-2"></i> حفظ العقد
                                        </button>
                                    </div>

                                    <!-- Collapsible JSON Section -->
                                    <div class="mt-3">
                                        <button class="btn btn-secondary w-100 mb-2" type="button" 
                                                data-bs-toggle="collapse" data-bs-target="#jsonSection" 
                                                aria-expanded="false" aria-controls="jsonSection">
                                            <i class="bi bi-code-square me-2"></i>
                                            عرض كود JSON
                                            <i class="bi bi-chevron-down ms-2"></i>
                                        </button>
                                        <div class="collapse" id="jsonSection">
                                            <textarea id="json_output" name="json_output" class="form-control" rows="10" readonly></textarea>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/theme.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="../assets/lib/datatables/jquery.dataTables.min.js"></script>
    <script src="../assets/lib/datatables/dataTables.bootstrap5.min.js"></script>
    <script src="../assets/js/sidebar.js"></script>
    <script>
        // Global variables for tasks and original data
        let tasks = [];
        let originalData = {};
        let evaluation = { percentageEvaluation: "no", workDaysEvaluation: "no" };

        $(document).ready(function() {
            // Initialize Select2 for project selection
            $('#project_id').select2({
                placeholder: 'اختر المشروع',
                allowClear: true,
                language: { noResults: () => "لا توجد نتائج" },
                dir: "rtl"
            });

            // Initialize Select2 for contract type
            $('#contract_type').select2({
                placeholder: 'اختر نوع العقد',
                allowClear: true,
                language: { noResults: () => "لا توجد نتائج" },
                dir: "rtl"
            });

            // Initialize Select2 for job title
            $('#job_title_id').select2({
                placeholder: 'اختر المسمى الوظيفي',
                allowClear: true,
                language: { noResults: () => "لا توجد نتائج" },
                dir: "rtl"
            });

            // Initialize Select2 for employee selection
            $('#employee_id').select2({
                placeholder: 'اختر الموظف',
                allowClear: true,
                language: { noResults: () => "لا توجد نتائج" },
                dir: "rtl",
                multiple: true,
                closeOnSelect: true,
                templateResult: function(data) {
                    if (!data.id) return data.text;
                    var $option = $(data.element);
                    var isSelected = $('#employee_id').val() && $('#employee_id').val().indexOf(data.id) > -1;
                    if (isSelected) {
                        return null;
                    }
                    return data.text;
                }
            }).on('select2:select', function() {
                // Close the dropdown after selection
                $(this).select2('close');
            }).on('click', function() {
                // Reopen the dropdown when clicking the field
                $(this).select2('open');
            });
            
            // Initialize Select2 for contract selector
            $('#contract_selector').select2({
                placeholder: 'اختر عقداً للتحديث',
                allowClear: true,
                language: { noResults: () => "لا توجد نتائج" },
                dir: "rtl",
                ajax: {
                    url: 'create_contract_con.php?action=get_contracts',
                    dataType: 'json',
                    delay: 250,
                    processResults: function(data) {
                        return {
                            results: data.data.map(function(contract) {
                                return {
                                    id: contract.id_contract,
                                    text: `<div style="display: flex; gap: 8px; padding: 4px 0;">
                                        <span class="contract-info">عقد رقم: ${contract.id_contract}</span>
                                        <span class="contract-info">الموظف: ${contract.name_ar_contract || 'غير محدد'}</span>
                                        <span class="contract-info">المشروع: ${contract.Project_name || 'غير محدد'}</span>
                                        <span class="contract-info">نوع العقد: ${contract.contract_type_name}</span>
                                    </div>`
                                };
                            })
                        };
                    },
                    cache: true
                },
                minimumInputLength: 0,
                escapeMarkup: function(markup) {
                    return markup;
                },
                templateResult: function(data) {
                    return data.text;
                },
                templateSelection: function(data) {
                    if (!data.id) return data.text;
                    // Strip HTML for selected option
                    return $(data.text).text().replace(/\s+/g, ' ').trim();
                }
            }).on('change', function() {
                // Enable/disable the view button based on selection
                if ($(this).val()) {
                    $('#viewContractBtn').prop('disabled', false);
                } else {
                    $('#viewContractBtn').prop('disabled', true);
                }
            });
            
            // View Contract button click
            $('#viewContractBtn').on('click', function() {
                const contractId = $('#contract_selector').val();
                if (!contractId) return;
                
                // Show loading indicator
                $(this).html('<i class="bi bi-hourglass-split"></i> جاري التحميل...');
                $(this).prop('disabled', true);
                
                // Fetch contract data
                $.ajax({
                    url: 'create_contract_con.php',
                    data: {
                        action: 'get_contract_data',
                        id: contractId
                    },
                    method: 'GET',
                    dataType: 'json',
                    success: function(data) {
                        console.log("Contract data received:", data); // Debug log
                        
                        // Store contract ID in hidden field
                        $('#contract_id').val(data.id_contract);
                        
                        // Set project
                        $('#project_id').val(data.id_Project).trigger('change');
                        
                        // Set employee when updating contract
                        // First clear the employee selection
                        $('#employee_id').val(null).trigger('change');
                        
                        // For updating existing contracts, we first select the employee from data
                        if (data.id_employees) {
                            // If we have the employee ID, try to find and select
                            $('#employee_id').val(data.id_employees).trigger('change');
                            // Disable employee selection during update
                            $('#employee_id').prop('disabled', true);
                        }
                        
                        // Set contract type and store original value for comparison
                        $('#contract_type').val(data.contract_type).trigger('change');
                        originalContractType = data.contract_type; // Store original contract type
                        
                        // Set dates - ensure the end date field is enabled if needed
                        $('#version_date').val(data.version_date);
                        $('#start_date_contract').val(data.start_date_contract);
                        
                        // Enable or disable end date based on contract type
                        if (data.contract_type == 2 || data.contract_type == 4) { // Closed contracts
                            $('#end_date_contract').prop('disabled', false);
                            $('#end_date_contract').val(data.end_date_contract);
                        } else { // Open contracts
                            $('#end_date_contract').prop('disabled', true);
                            $('#end_date_contract').val('');
                        }
                        
                        // Set monetary details
                        $('#wage_contract').val(data.wage_contract);
                        $('#currency_type').val(data.Type_currency).trigger('change');
                        
                        // Set amount written fields with logging
                        console.log("Loading contract data - amount_written_ar:", data.amount_written_ar);
                        console.log("Loading contract data - amount_written_en:", data.amount_written_en);
                        
                        const amountEn = data.amount_written_en || '';
                        
                        // Set both visible and hidden fields
                        $('#amount_written_ar').val(data.amount_written_ar || '');
                        $('#amount_written_en').val(amountEn);
                        $('#amount_written_en_hidden').val(amountEn);
                        
                        // Ensure fields are populated even if automatic conversion doesn't work
                        if (!data.amount_written_ar && data.wage_contract && data.Type_currency) {
                            updateAmountWritten();
                        } else {
                            // Make sure to save the value to the hidden field
                            saveAmountWrittenEn(amountEn);
                        }
                        
                        // Handle job title and tasks
                        // Find job title ID by name
                        let jobTitleId = null;
                        $('#job_title_id option').each(function() {
                            const optionText = $(this).text();
                            if (data.name_Job && optionText.includes(data.name_Job)) {
                                jobTitleId = $(this).val();
                                return false; // break the loop
                            }
                        });
                        
                        if (jobTitleId) {
                            $('#job_title_id').val(jobTitleId).trigger('change');
                        }
                        
                        // Handle task data separately to avoid timing issues
                        setTimeout(() => {
                            try {
                                // Set JSON data regardless of job title
                                if (data.data_todo_list_contract) {
                                    $('#json_output').val(data.data_todo_list_contract);
                                    
                                    // Parse task data
                                    const taskData = JSON.parse(data.data_todo_list_contract);
                                    originalData = taskData;
                                    
                                    if (taskData && taskData.jobDetails && Array.isArray(taskData.jobDetails.tasks)) {
                                        tasks = taskData.jobDetails.tasks;
                                        renderTaskList();
                                    } else {
                                        console.warn("Tasks data format is not as expected");
                                        tasks = [];
                                        renderTaskList();
                                    }
                                }
                            } catch (e) {
                                console.error("Error parsing task data:", e);
                                alert("حدث خطأ أثناء تحليل بيانات المهام. يرجى التحقق من تنسيق البيانات.");
                                tasks = [];
                                renderTaskList();
                            }
                        }, 500);
                        
                        // Update form buttons and title
                        $('.save-contract-btn').html('<i class="bi bi-pencil-square me-2"></i> تحديث بيانات العقد');
                        $('.save-contract-btn').removeClass('btn-primary').addClass('btn-success');
                        $('.card-title').text('تحديث بيانات العقد');
                        
                        // Reset view button
                        $('#viewContractBtn').html('<i class="bi bi-eye-fill"></i> عرض بيانات العقد');
                        $('#viewContractBtn').prop('disabled', false);
                        
                        // Enable the task add button
                        $('#add_task_btn').prop('disabled', false);
                    },
                    error: function(xhr, status, error) {
                        // Show error
                        alert('حدث خطأ أثناء استرجاع بيانات العقد: ' + (xhr.responseJSON?.error || error));
                        
                        // Reset view button
                        $('#viewContractBtn').html('<i class="bi bi-eye-fill"></i> عرض بيانات العقد');
                        $('#viewContractBtn').prop('disabled', false);
                    }
                });
            });
            
            // Reset Form button click
            $('#resetFormBtn').on('click', function() {
                resetForm();
            });
            
            // Helper function to reset form
            function resetForm() {
                // Reset contract ID
                $('#contract_id').val('0');
                
                // Reset form fields
                document.getElementById('contractForm').reset();
                
                // Re-enable employee selection
                $('#employee_id').prop('disabled', false);
                
                // Reset Select2 dropdowns
                $('#project_id').val('').trigger('change');
                $('#employee_id').val(null).trigger('change');
                $('#contract_type').val('').trigger('change');
                $('#job_title_id').val('').trigger('change');
                $('#currency_type').val('').trigger('change');
                $('#contract_selector').val('').trigger('change');
                
                // Reset tasks
                tasks = [];
                originalData = {};
                originalContractType = null; // Reset original contract type
                renderTaskList();
                updateJsonOutput();
                
                // Reset button text and form title
                $('.save-contract-btn').html('<i class="bi bi-save me-2"></i> حفظ العقد');
                $('.save-contract-btn').removeClass('btn-success').addClass('btn-primary');
                $('.card-title').text('إنشاء عقد جديد');
                
                // Disable the task add button
                $('#add_task_btn').prop('disabled', true);
            }
        });

        // Handle identity type selection
        function updateIdentityType() {
            const select = document.getElementById('identity_type');
            const arInput = document.getElementById('Identity_contract_ar');
            const enInput = document.getElementById('Identity_contract_en');
            const typeEnInput = document.getElementById('Identity_type_en');
            
            if (select.value === 'Passport') {
                arInput.value = 'جواز سفر';
                enInput.value = 'Passport';
                typeEnInput.value = 'Passport';
            } else if (select.value === 'National ID') {
                arInput.value = 'بطاقة شخصية';
                enInput.value = 'National ID';
                typeEnInput.value = 'National ID';
            } else {
                arInput.value = '';
                enInput.value = '';
                typeEnInput.value = '';
            }
        }

        // Handle place of issuance selection
        function handleIssuePlaceChange() {
            const select = document.getElementById('identity_issue_place');
            const customInput = document.getElementById('custom_issue_place');
            const arInput = document.getElementById('Identity_issue_contract_ar');
            const enInput = document.getElementById('Identity_issue_contract_en');
            const placeEnInput = document.getElementById('Identity_issue_place_en');

            if (select.value === 'custom') {
                customInput.style.display = 'block';
                customInput.required = true;
                arInput.value = customInput.value;
                enInput.value = customInput.value;
                placeEnInput.value = customInput.value;
                placeEnInput.readOnly = false;
            } else if (select.value) {
                customInput.style.display = 'none';
                customInput.required = false;
                const [en, ar] = select.value.split('|');
                arInput.value = ar;
                enInput.value = en;
                placeEnInput.value = en;
                placeEnInput.readOnly = true;
            } else {
                customInput.style.display = 'none';
                customInput.required = false;
                arInput.value = '';
                enInput.value = '';
                placeEnInput.value = '';
                placeEnInput.readOnly = true;
            }
        }

        // Handle custom place input
        function handleCustomPlaceInput() {
            const customInput = document.getElementById('custom_issue_place');
            const arInput = document.getElementById('Identity_issue_contract_ar');
            const enInput = document.getElementById('Identity_issue_contract_en');
            const placeEnInput = document.getElementById('Identity_issue_place_en');
            
            arInput.value = customInput.value;
            enInput.value = customInput.value;
            placeEnInput.value = customInput.value;
        }

        // Save the amount_written_en value to the hidden field
        function saveAmountWrittenEn(value) {
            document.getElementById('amount_written_en_hidden').value = value;
            console.log("Saved amount_written_en to hidden field:", value);
        }
        
        // Handle amount written update
        function updateAmountWritten() {
            const amount = document.getElementById('wage_contract').value;
            const currencyType = document.getElementById('currency_type').value;
            const arField = document.getElementById('amount_written_ar');
            const enField = document.getElementById('amount_written_en');

            if (!amount || !currencyType) {
                arField.value = '';
                enField.value = '';
                return;
            }

            const amountNum = parseFloat(amount);
            if (isNaN(amountNum)) {
                arField.value = '';
                enField.value = '';
                return;
            }

            // Split the amount into integer and decimal parts
            const integerPart = Math.floor(amountNum);
            const decimalPart = Math.round((amountNum - integerPart) * 100); // Get cents/fils

            // Convert integer part to words
            const arIntegerAmount = numberToArabicWords(integerPart);
            const enIntegerAmount = numberToEnglishWords(integerPart);

            let arAmount = arIntegerAmount;
            let enAmount = enIntegerAmount;

            // Add decimal part if it exists
            if (decimalPart > 0) {
                const arDecimalAmount = numberToArabicWords(decimalPart);
                const enDecimalAmount = numberToEnglishWords(decimalPart);

                if (currencyType === '1') { // Dollar
                    arAmount += " دولاراً و" + arDecimalAmount + " سنتاً";
                    enAmount += " dollars and " + enDecimalAmount + " cents";
                } else { // Riyal
                    arAmount += " ريالاً و" + arDecimalAmount + " هللة";
                    enAmount += " riyals and " + enDecimalAmount + " halalas";
                }
            } else {
                // No decimal part
                if (currencyType === '1') { // Dollar
                    arAmount += " دولاراً";
                    enAmount += " dollars";
                } else { // Riyal
                    arAmount += " ريالاً";
                    enAmount += " riyals";
                }
            }

            // Update fields
            arField.value = arAmount;
            enField.value = enAmount;

            // Also save to hidden field
            saveAmountWrittenEn(enField.value);
        }

        // Convert number to Arabic words
        function numberToArabicWords(number) {
            const digits = ['صفر', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة'];
            const teens = ['عشرة', 'أحد عشر', 'اثنا عشر', 'ثلاثة عشر', 'أربعة عشر', 'خمسة عشر', 'ستة عشر', 'سبعة عشر', 'ثمانية عشر', 'تسعة عشر'];
            const tens = ['', '', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون'];
            const hundreds = ['', 'مائة', 'مئتان', 'ثلاثمائة', 'أربعمائة', 'خمسمائة', 'ستمائة', 'سبعمائة', 'ثمانمائة', 'تسعمائة'];
            
            function convertHundreds(num) {
                if (num === 0) return '';
                
                let words = '';
                
                // Handle hundreds
                if (num >= 100) {
                    words += hundreds[Math.floor(num / 100)];
                    num %= 100;
                    if (num > 0) words += ' و';
                }
                
                // Handle tens and ones
                if (num > 0) {
                    if (num < 10) {
                        words += digits[num];
                    } else if (num < 20) {
                        words += teens[num - 10];
                    } else {
                        const onesDigit = num % 10;
                        const tensDigit = Math.floor(num / 10);
                        if (onesDigit > 0) {
                            words += digits[onesDigit] + ' و';
                        }
                        words += tens[tensDigit];
                    }
                }
                
                return words;
            }

            if (number === 0) return digits[0];
            
            let words = '';
            
            // Handle billions
            if (number >= 1000000000) {
                const billions = Math.floor(number / 1000000000);
                if (billions === 1) {
                    words += 'مليار';
                } else if (billions === 2) {
                    words += 'ملياران';
                } else if (billions >= 3 && billions <= 10) {
                    words += convertHundreds(billions) + ' مليارات';
                } else {
                    words += convertHundreds(billions) + ' مليار';
                }
                number %= 1000000000;
                if (number > 0) words += ' و';
            }
            
            // Handle millions
            if (number >= 1000000) {
                const millions = Math.floor(number / 1000000);
                if (millions === 1) {
                    words += 'مليون';
                } else if (millions === 2) {
                    words += 'مليونان';
                } else if (millions >= 3 && millions <= 10) {
                    words += convertHundreds(millions) + ' ملايين';
                } else {
                    words += convertHundreds(millions) + ' مليون';
                }
                number %= 1000000;
                if (number > 0) words += ' و';
            }
            
            // Handle thousands
            if (number >= 1000) {
                const thousands = Math.floor(number / 1000);
                if (thousands === 1) {
                    words += 'ألف';
                } else if (thousands === 2) {
                    words += 'ألفان';
                } else if (thousands >= 3 && thousands <= 10) {
                    words += convertHundreds(thousands) + ' آلاف';
                } else {
                    words += convertHundreds(thousands) + ' ألف';
                }
                number %= 1000;
                if (number > 0) words += ' و';
            }
            
            // Handle remaining hundreds
            if (number > 0) {
                words += convertHundreds(number);
            }
            
            return words;
        }

        // Convert number to English words
        function numberToEnglishWords(number) {
            const ones = ['', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight', 'nine'];
            const teens = ['ten', 'eleven', 'twelve', 'thirteen', 'fourteen', 'fifteen', 'sixteen', 'seventeen', 'eighteen', 'nineteen'];
            const tens = ['', '', 'twenty', 'thirty', 'forty', 'fifty', 'sixty', 'seventy', 'eighty', 'ninety'];

            function capitalizeFirst(str) {
                return str.charAt(0).toUpperCase() + str.slice(1);
            }

            function convertLessThanThousand(num) {
                if (num === 0) return '';
                
                let words = '';
                
                // Handle hundreds
                if (num >= 100) {
                    words += ones[Math.floor(num / 100)] + ' hundred';
                    num %= 100;
                    if (num > 0) words += ' and ';
                }
                
                // Handle tens and ones
                if (num > 0) {
                    if (num < 10) {
                        words += ones[num];
                    } else if (num < 20) {
                        words += teens[num - 10];
                    } else {
                        const tensDigit = Math.floor(num / 10);
                        const onesDigit = num % 10;
                        words += tens[tensDigit];
                        if (onesDigit > 0) {
                            words += '-' + ones[onesDigit];
                        }
                    }
                }
                
                return words;
            }

            if (number === 0) return 'zero';

            let words = '';
            
            // Handle billions
            if (number >= 1000000000) {
                const billions = Math.floor(number / 1000000000);
                words += convertLessThanThousand(billions) + ' billion';
                number %= 1000000000;
                if (number > 0) words += ' ';
            }
            
            // Handle millions
            if (number >= 1000000) {
                const millions = Math.floor(number / 1000000);
                words += convertLessThanThousand(millions) + ' million';
                number %= 1000000;
                if (number > 0) words += ' ';
            }
            
            // Handle thousands
            if (number >= 1000) {
                const thousands = Math.floor(number / 1000);
                words += convertLessThanThousand(thousands) + ' thousand';
                number %= 1000;
                if (number > 0) words += ' ';
            }

            // Handle rest of the number
            if (number > 0) {
                words += convertLessThanThousand(number);
            }

            return capitalizeFirst(words.trim());
        }

        $(document).ready(function() {
            // Suppress DataTables warning alerts
            $.fn.dataTable.ext.errMode = 'none';
            
            // Destroy existing DataTable instance if it exists
            if ($.fn.DataTable.isDataTable('#contracts-table')) {
                $('#contracts-table').DataTable().destroy();
            }
            
            // Initialize DataTable for contracts
            $('#contracts-table').DataTable({
                processing: true,
                serverSide: false,
                searching: false, // Disable built-in search as we'll implement our own
                paging: false, // Disable pagination to show all records
                info: false, // Hide information display
                autoWidth: false,
                responsive: true,
                language: {
                    "emptyTable": "لا توجد عقود متاحة",
                    "info": "عرض _START_ إلى _END_ من _TOTAL_ عقد",
                    "infoEmpty": "عرض 0 إلى 0 من 0 عقد",
                    "infoFiltered": "(تمت تصفيته من _MAX_ عقد)",
                    "lengthMenu": "عرض _MENU_ عقد",
                    "loadingRecords": "جاري التحميل...",
                    "processing": "جاري المعالجة...",
                    "search": "بحث:",
                    "zeroRecords": "لم يتم العثور على عقود مطابقة",
                    "paginate": {
                        "first": "الأول",
                        "last": "الأخير",
                        "next": "التالي",
                        "previous": "السابق"
                    }
                },
                ajax: {
                    url: 'create_contract_con.php?action=get_contracts',
                    dataSrc: 'data',
                    data: function(d) {
                        d.search = $('#contract-search').val();
                        d.project_id = $('#project-filter').val();
                        return d;
                    }
                },
                columns: [
                    { data: 'id_contract' },
                    { data: 'name_ar_contract' }, // Display only Arabic name
                    { data: 'id_Project' },
                    { data: 'contract_type_name' },
                    { data: 'add_contract' }
                ],
                order: [[0, 'desc']], // Sort by contract ID descending
                drawCallback: function() {
                    // Apply theme to table after drawing
                    const theme = $('body').attr('data-theme');
                    if (theme === 'dark') {
                        $('#contracts-table').addClass('table-dark');
                    } else {
                        $('#contracts-table').removeClass('table-dark');
                    }
                }
            });
            
            // Handle search input
            $('#contract-search').on('keyup', function() {
                contractsTable.ajax.reload();
            });
            
            // Handle project filter change
            $('#project-filter').on('change', function() {
                contractsTable.ajax.reload();
            });
            
            // Initialize Select2 for project filter
            $('#project-filter').select2({
                placeholder: 'تصفية حسب المشروع',
                allowClear: true,
                language: { noResults: () => "لا توجد نتائج" },
                dir: "rtl"
            });
        });

        // عند تغيير نوع العقد
        function handleContractTypeChange() {
            const contractType = document.getElementById('contract_type').value;
            const endDateInput = document.getElementById('end_date_contract');
            const jobTitleField = document.getElementById('job_title_id');
            const addTaskButton = document.getElementById('add_task_btn');

            // Handle end date validation
            if (!contractType) {
                // No contract type selected
                endDateInput.disabled = true;
                endDateInput.value = '';
            } else if (contractType === '1' || contractType === '3') {
                // Monthly or Daily Open Contract
                endDateInput.disabled = true;
                endDateInput.value = ''; // Clear the value
            } else if (contractType === '2' || contractType === '4') {
                // Monthly or Daily Closed Contract
                endDateInput.disabled = false;
            }

            // Handle job title field
            if (contractType) {
                jobTitleField.disabled = false; // Enable job title field
            } else {
                jobTitleField.disabled = true; // Disable job title field
                jobTitleField.value = ""; // Reset job title
                tasks = [];
                originalData = {};
                addTaskButton.disabled = true; // Disable add task button
                renderTaskList();
                updateJsonOutput();
            }

            updateEvaluation(); // Update evaluation code
        }

        // عند تغيير الاسم الوظيفي
        function handleJobTitleChange() {
            const jobTitleField = document.getElementById('job_title_id');
            const addTaskButton = document.getElementById('add_task_btn');
            const jobTitleDisplay = document.getElementById('selected_job_title_display');
            const jobTitleAr = document.getElementById('job_title_ar');
            const jobTitleEn = document.getElementById('job_title_en');

            if (jobTitleField.value) {
                const selectedOption = jobTitleField.options[jobTitleField.selectedIndex];
                const nameAr = selectedOption.text.split(' - ')[0];
                const nameEn = selectedOption.getAttribute('data-name-en');

                // Display the job titles
                jobTitleAr.textContent = nameAr;
                jobTitleEn.textContent = nameEn;
                jobTitleDisplay.style.display = 'block';

                addTaskButton.disabled = false; // Enable add task button
                updateTaskList(); // Update task list
            } else {
                jobTitleDisplay.style.display = 'none';
                addTaskButton.disabled = true; // Disable add task button
                tasks = [];
                originalData = {};
                renderTaskList();
                updateJsonOutput();
            }
        }

        // تحديث قسم evaluation بناءً على نوع العقد
        function updateEvaluation() {
            const contractType = document.getElementById('contract_type').value;

            if (contractType === "1" || contractType === "2") {
                evaluation = { percentageEvaluation: "yes", workDaysEvaluation: "no" };
            } else if (contractType === "3" || contractType === "4") {
                evaluation = { percentageEvaluation: "no", workDaysEvaluation: "yes" };
            } else {
                evaluation = { percentageEvaluation: "no", workDaysEvaluation: "no" };
            }

            updateJsonOutput(); // Update JSON output
        }

        // عرض المهام في القائمة
        function renderTaskList() {
            const taskList = document.getElementById('task_list');
            taskList.innerHTML = '';

            tasks.forEach((task, index) => {
                const taskDiv = document.createElement('div');
                taskDiv.className = 'task-item';

                const taskLabel = document.createElement('div');
                taskLabel.className = 'task-number mb-2';
                taskLabel.textContent = `المهمة ${index + 1}`;
                taskLabel.style.fontWeight = 'bold';
                taskLabel.style.color = 'var(--primary-color)';

                const inputContainer = document.createElement('div');
                inputContainer.className = 'task-input-container';

                // Arabic task name input
                const arInput = document.createElement('input');
                arInput.type = 'text';
                arInput.className = 'form-control mb-2';
                arInput.value = task.taskName || '';
                arInput.placeholder = 'اسم المهمة بالعربية';
                arInput.dir = 'rtl';
                arInput.oninput = () => updateTask(index, 'taskName', arInput.value);

                // English task name input
                const enInput = document.createElement('input');
                enInput.type = 'text';
                enInput.className = 'form-control mb-2';
                enInput.value = task.taskNameEn || '';
                enInput.placeholder = 'Task name in English';
                enInput.dir = 'ltr';
                enInput.oninput = () => updateTask(index, 'taskNameEn', enInput.value);

                // Delete button container
                const deleteContainer = document.createElement('div');
                deleteContainer.className = 'text-end'; // Align button to the right

                // Delete button
                const deleteButton = document.createElement('button');
                deleteButton.type = 'button';
                deleteButton.className = 'btn btn-danger btn-sm';
                deleteButton.innerHTML = '<i class="bi bi-trash"></i>';
                deleteButton.onclick = () => deleteTask(index);
                deleteButton.title = 'حذف المهمة'; // Add tooltip

                deleteContainer.appendChild(deleteButton);

                inputContainer.appendChild(taskLabel);
                inputContainer.appendChild(arInput);
                inputContainer.appendChild(enInput);
                inputContainer.appendChild(deleteContainer);
                taskDiv.appendChild(inputContainer);
                taskList.appendChild(taskDiv);
            });
        }

        // تحديث المهمة
        function updateTask(index, field, newValue) {
            tasks[index][field] = newValue;
            tasks[index].completionRate = 0; // Always set completionRate to 0
            updateJsonOutput();
        }

        // حذف المهمة
        function deleteTask(index) {
            tasks.splice(index, 1);
            renderTaskList();
            updateJsonOutput();
        }

        // إضافة مهمة جديدة
        function addNewTask(event) {
            if (event) {
                event.preventDefault();
            }
            tasks.push({
                taskName: '',
                taskNameEn: '',
                completionRate: 0 // Default to 0
            });
            renderTaskList();
            updateJsonOutput();
        }

        // تحديث قائمة المهام بناءً على الاسم الوظيفي
        function updateTaskList() {
            const select = document.getElementById('job_title_id');
            const selectedOption = select.options[select.selectedIndex];

            if (select.value) {
                const json = selectedOption.getAttribute('data-json') || '{}';
                const data = JSON.parse(json);

                originalData = data;
                tasks = (data.jobDetails?.tasks || []).map(task => ({
                    taskName: task.taskName || '',
                    taskNameEn: task.taskNameEn || '',
                    completionRate: 0 // Always set to 0 when loading
                }));
                renderTaskList();
                updateJsonOutput();
            } else {
                tasks = [];
                originalData = {};
                renderTaskList();
                updateJsonOutput();
            }
        }

        // تحديث كود JSON
        function updateJsonOutput() {
            const jsonOutput = document.getElementById('json_output');
            const updatedData = {
                ...originalData,
                evaluation: evaluation,
                jobDetails: {
                    ...originalData.jobDetails,
                    tasks: tasks.map(task => ({
                        taskName: task.taskName,
                        taskNameEn: task.taskNameEn,
                        completionRate: task.completionRate
                    }))
                }
            };
            jsonOutput.value = JSON.stringify(updatedData, null, 2);
        }
        
        // التحقق من تاريخ البداية
        function handleStartDateChange() {
            const startDate = document.getElementById('start_date_contract').value;
            
            // If end date is enabled and has a value, validate that it's after start date
            const endDateInput = document.getElementById('end_date_contract');
            if (!endDateInput.disabled && endDateInput.value) {
                if (endDateInput.value <= startDate) {
                    alert("تاريخ النهاية يجب أن يكون بعد تاريخ البداية");
                    endDateInput.value = '';
                }
            }
        }
        
        // التحقق من تاريخ النهاية
        function handleEndDateChange() {
            const startDate = document.getElementById('start_date_contract').value;
            const endDate = document.getElementById('end_date_contract').value;
            
            if (endDate && startDate && endDate <= startDate) {
                alert("تاريخ النهاية يجب أن يكون بعد تاريخ البداية");
                document.getElementById('end_date_contract').value = '';
            }
        }
        


        // Variable to store original contract type for comparison
        let originalContractType = null;

        // التحقق من صحة النموذج قبل الإرسال
        function validateForm() {
            const contractId = $('#contract_id').val();
            const isUpdate = contractId > 0;

            // Check if contract type has changed and show confirmation dialog
            if (isUpdate && originalContractType !== null) {
                const currentContractType = parseInt($('#contract_type').val());
                const originalType = parseInt(originalContractType);

                if (originalType !== currentContractType) {
                    // Check if there's an open/closed state change
                    // Open states: 1 (Monthly Open), 3 (Daily Open)
                    // Closed states: 2 (Monthly Closed), 4 (Daily Closed)
                    const originalIsOpen = (originalType === 1 || originalType === 3);
                    const currentIsOpen = (currentContractType === 1 || currentContractType === 3);

                    let confirmMessage = 'تحذير: سيؤدي تغيير نوع العقد إلى حذف جميع تقارير الإنجاز المرتبطة بهذا العقد (إن وجدت) ويجب إعادة إنشاؤها.';

                    // Add additional warning if open/closed state is changing
                    if (originalIsOpen !== currentIsOpen) {
                        confirmMessage += '\n\nتحذير إضافي: سيؤدي تغيير العقد من مفتوح إلى مغلق أو العكس إلى حذف جميع سجلات الحضور المرتبطة بهذا العقد أيضاً.';
                    }

                    confirmMessage += '\n\nهل تريد المتابعة؟';

                    if (!confirm(confirmMessage)) {
                        return false; // User cancelled the operation
                    }
                }
            }

            // Only validate employee selection for new contracts
            if (!isUpdate) {
                const selectedEmployees = $('#employee_id').val();
                if (!selectedEmployees || selectedEmployees.length === 0) {
                    alert('يرجى اختيار موظف واحد على الأقل');
                    return false;
                }
            }

            if (tasks.length === 0) {
                alert('يجب إضافة مهمة واحدة على الأقل');
                return false;
            }

            for (let i = 0; i < tasks.length; i++) {
                if (!tasks[i].taskName || tasks[i].taskName.trim() === '') {
                    alert('لا يمكن حفظ العقد. يوجد حقل مهمة عربي فارغ');
                    return false;
                }
                if (!tasks[i].taskNameEn || tasks[i].taskNameEn.trim() === '') {
                    alert('لا يمكن حفظ العقد. يوجد حقل مهمة إنجليزي فارغ');
                    return false;
                }
            }

            const jsonOutput = document.getElementById('json_output').value;
            if (!jsonOutput) {
                alert('يجب إدخال بيانات JSON للمهام');
                return false;
            }

            // Check for amount_written fields
            const amountWrittenAr = document.getElementById('amount_written_ar').value;
            const amountWrittenEn = document.getElementById('amount_written_en').value;
            
            if (!amountWrittenAr || amountWrittenAr.trim() === '') {
                alert('يرجى إدخال المبلغ بالعربية');
                return false;
            }
            
            if (!amountWrittenEn || amountWrittenEn.trim() === '') {
                alert('يرجى إدخال المبلغ بالإنجليزية');
                return false;
            }
            
            // Log values before submission for debugging
            console.log("Form submission - amount_written_en:", amountWrittenEn);
            
            // Force set the amount_written_en field one more time before submission
            // This is a workaround to ensure the value is correctly submitted
            document.getElementById('amount_written_en').value = amountWrittenEn;
            
            // Add a hidden field as a backup
            if (isUpdate) {
                const hiddenField = document.createElement('input');
                hiddenField.type = 'hidden';
                hiddenField.name = 'amount_written_en_backup';
                hiddenField.value = amountWrittenEn;
                document.getElementById('contractForm').appendChild(hiddenField);
                
                console.log("Added backup field with value:", amountWrittenEn);
            }
            
            return true;
        }
    </script>
</body>
</html>

